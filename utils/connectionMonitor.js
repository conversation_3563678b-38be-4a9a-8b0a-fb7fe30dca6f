const { sequelize } = require('../config/database');

class ConnectionMonitor {
  constructor() {
    this.connectionStats = {
      active: 0,
      idle: 0,
      total: 0,
      errors: 0,
      lastError: null,
    };
    
    this.startMonitoring();
  }

  startMonitoring() {
    // Monitor connection pool every 30 seconds
    setInterval(() => {
      this.logConnectionStats();
    }, 30000);

    // Hook into Sequelize's error handling to track connection events
    const originalError = console.error;

    // Override console.error to capture Sequelize connection events
    console.error = (...args) => {
      const message = args.join(' ');
      if (message.includes('max_user_connections') || message.includes('SequelizeConnectionError')) {
        this.connectionStats.errors++;
        this.connectionStats.lastError = {
          message: message,
          timestamp: new Date().toISOString(),
        };

        if (message.includes('max_user_connections')) {
          originalError('⚠️  CONNECTION LIMIT REACHED - Consider implementing connection throttling');
        }
      }
      originalError.apply(console, args);
    };

    // Log initial status
    console.log('🔍 Connection monitoring started');
  }

  logConnectionStats() {
    try {
      const connectionManager = sequelize.connectionManager;
      const pool = connectionManager.pool;

      if (pool) {
        // Get pool statistics - different properties available in Sequelize v6
        const stats = {
          size: pool.size || 0,
          available: pool.available || 0,
          using: pool.using || 0,
          waiting: pool.waiting || 0,
          min: pool.min || 0,
          max: pool.max || 0,
        };

        console.log('📊 Connection Pool Stats:', {
          ...stats,
          errors: this.connectionStats.errors,
          lastError: this.connectionStats.lastError,
        });

        // Warn if connection usage is high (80% of max)
        const usagePercentage = stats.max > 0 ? (stats.using / stats.max) * 100 : 0;
        if (usagePercentage >= 80) {
          console.warn('⚠️  High connection usage detected:', {
            ...stats,
            usagePercentage: `${usagePercentage.toFixed(1)}%`
          });
        }
      } else {
        console.log('📊 Connection Pool: Not available or not initialized');
      }
    } catch (error) {
      console.error('Error getting connection stats:', error.message);
    }
  }

  async checkConnectionHealth() {
    try {
      await sequelize.authenticate();
      return { healthy: true, message: 'Database connection is healthy' };
    } catch (error) {
      return { 
        healthy: false, 
        message: error.message,
        isConnectionLimit: error.message.includes('max_user_connections')
      };
    }
  }

  getStats() {
    try {
      const pool = sequelize.connectionManager.pool;
      return {
        pool: pool ? {
          size: pool.size || 0,
          available: pool.available || 0,
          using: pool.using || 0,
          waiting: pool.waiting || 0,
          min: pool.min || 0,
          max: pool.max || 0,
        } : null,
        errors: this.connectionStats.errors,
        lastError: this.connectionStats.lastError,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        pool: null,
        errors: this.connectionStats.errors,
        lastError: this.connectionStats.lastError,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  // Helper method to execute database operations with connection monitoring
  async executeWithMonitoring(operation, operationName = 'database operation') {
    const startTime = Date.now();
    
    try {
      console.log(`🔄 Starting ${operationName}`);
      const result = await operation();
      const duration = Date.now() - startTime;
      console.log(`✅ Completed ${operationName} in ${duration}ms`);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ Failed ${operationName} after ${duration}ms:`, error.message);
      
      if (error.message.includes('max_user_connections')) {
        console.error('🚨 Connection limit exceeded during:', operationName);
        this.logConnectionStats();
      }
      
      throw error;
    }
  }
}

// Create singleton instance
const connectionMonitor = new ConnectionMonitor();

module.exports = connectionMonitor;
