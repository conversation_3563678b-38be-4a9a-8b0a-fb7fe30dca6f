const express = require('express');
const app = express();

const AppRoutes = require('./app');
const CmsRoutes = require('./cms');
const HealthRoutes = require('./health.routes');
const { STATUS_CODES } = require('../config/constants');

module.exports = (app) => {
  app.get('/', (req, res) => {
    console.log(
      'Welcome to ' + process.env.NODE_ENV + ' ' + process.env.APP_NAME
    );

    res
      .status(STATUS_CODES.SUCCESS)
      .send('Welcome to ' + process.env.NODE_ENV + ' ' + process.env.APP_NAME);
  });
  app.use(AppRoutes);
  app.use(CmsRoutes);
  app.use(HealthRoutes);
};
