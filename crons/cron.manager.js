const cron = require('node-cron');
const { SCHEDULES } = require('../config/cron.config');
const DataUpdateCron = require('./jobs/dataUpdate.cron');

class CronManager {
  constructor() {
    this.jobs = new Map();
    this.dataUpdateCron = new DataUpdateCron();
  }

  initializeJobs() {
    //     this.scheduleJob('updateCompanyStats', SCHEDULES.UPDATE_COMPANY_STATS,
    //       () => this.companyStatsCron.execute());

    //     this.scheduleJob('cleanupOldData', SCHEDULES.CLEANUP_OLD_DATA,
    //       () => this.cleanupCron.execute());

    this.scheduleJob(
      'dataUpdate',
      SCHEDULES.EVERY_10_MINUTES, // Runs every 1st and 16th day of the month
      () => this.dataUpdateCron.execute()
    );

    // this.dataUpdateCron.execute();
  }

  scheduleJob(name, schedule, callback) {
    if (this.jobs.has(name)) {
      this.jobs.get(name).stop();
    }
    const job = cron.schedule(schedule, callback);
    this.jobs.set(name, job);
  }

  stopJob(name) {
    if (this.jobs.has(name)) {
      this.jobs.get(name).stop();
      this.jobs.delete(name);
    }
  }

  stopAllJobs() {
    for (const job of this.jobs.values()) {
      job.stop();
    }
    this.jobs.clear();
  }
}

module.exports = CronManager;
