const connectionMonitor = require('../utils/connectionMonitor');

class HealthController {
  async checkHealth(req, res) {
    try {
      const dbHealth = await connectionMonitor.checkConnectionHealth();
      const connectionStats = connectionMonitor.getStats();
      
      const healthStatus = {
        status: dbHealth.healthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        database: {
          connected: dbHealth.healthy,
          message: dbHealth.message,
          connectionLimit: dbHealth.isConnectionLimit || false,
        },
        connectionPool: connectionStats.pool,
        errors: {
          count: connectionStats.errors,
          lastError: connectionStats.lastError,
        },
        uptime: process.uptime(),
        memory: process.memoryUsage(),
      };

      const statusCode = dbHealth.healthy ? 200 : 503;
      
      return res.status(statusCode).json(healthStatus);
    } catch (error) {
      console.error('Health check error:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Health check failed',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async checkDatabaseConnections(req, res) {
    try {
      const stats = connectionMonitor.getStats();
      
      return res.json({
        connectionPool: stats.pool,
        errors: stats.errors,
        lastError: stats.lastError,
        recommendations: this.getConnectionRecommendations(stats),
      });
    } catch (error) {
      console.error('Connection check error:', error);
      return res.status(500).json({
        error: 'Failed to check database connections',
        message: error.message,
      });
    }
  }

  getConnectionRecommendations(stats) {
    const recommendations = [];
    
    if (stats.pool) {
      const { using, size } = stats.pool;
      const usagePercentage = (using / size) * 100;
      
      if (usagePercentage > 80) {
        recommendations.push('High connection usage detected. Consider optimizing database queries or increasing connection pool size.');
      }
      
      if (stats.pool.waiting > 0) {
        recommendations.push('Connections are waiting in queue. Consider reducing concurrent database operations.');
      }
    }
    
    if (stats.errors > 10) {
      recommendations.push('High number of connection errors detected. Check database server health and network connectivity.');
    }
    
    if (stats.lastError && stats.lastError.message.includes('max_user_connections')) {
      recommendations.push('Connection limit exceeded. Consider implementing connection throttling or increasing MySQL max_user_connections.');
    }
    
    return recommendations;
  }
}

module.exports = HealthController;
